import { CommonActions } from '@react-navigation/native';
import Safe, { getSafeAddressFromDeploymentTx, PredictedSafeProps } from '@safe-global/protocol-kit';
import * as Clipboard from 'expo-clipboard';
import { useNavigation } from 'expo-router';
import { useState } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import { privateKeyToAddress } from 'viem/accounts';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import TextInput from '@/components/ui/TextInput';
import { DEFAULT_VALIDITY_BLOCKS, RecoveryPurpose, TRecoveryKey, useAddRecoveryKeys } from '@/hooks/useAddRecoveryKeys';
import { TWallet, useCreateWallet } from '@/hooks/useCreateWallet';
import { useDeploySafe } from '@/hooks/useDeploySafe';
import { useEnableSafeModule } from '@/hooks/useEnableSafeModule';
import { useGetLocation } from '@/hooks/useGetLocation';
import { useGetRecoveryKeysNewWallet } from '@/hooks/useGetRecoveryKeysNewWallet';
import { useGetSafeConfig } from '@/hooks/useGetSafeConfig';
import { useRegisterSafe } from '@/hooks/useRegisterSafe';
import { useSetupRecoveryKey } from '@/hooks/useSetupRecoveryKey';
import { useSetupWallet } from '@/hooks/useSetupWallet';
import { useTheme } from '@/hooks/useThemeColor';
import { useWalletStore } from '@/store/wallet';
import { env } from '@/utils/env';
import { CURRENT_CHAIN, pkToAccount } from '@/utils/web3';

type Props = {};

type TSigner = TWallet & { address: string };

export const SetupWallet = (props: Props) => {
  const { styles } = useStyles();
  const navigation = useNavigation();
  const [signers, setSigners] = useState<TSigner[]>([]);
  const [threshold, setThreshold] = useState<string>('');

  const [isPending, setIsPending] = useState(false);

  const { data: recoveryKeys = [] } = useGetRecoveryKeysNewWallet();
  const { mutateAsync: createWallet } = useCreateWallet();
  const { mutateAsync: setupWallet } = useSetupWallet();
  const { mutateAsync: getLocation } = useGetLocation();
  const { mutateAsync: deploySafe, isPending: isDeployingSafe } = useDeploySafe();
  const { setSafeWallet } = useWalletStore();
  const { mutateAsync: registerSafe } = useRegisterSafe();
  const { mutateAsync: enableSafeModule } = useEnableSafeModule();
  const { mutateAsync: addRecoveryKeys } = useAddRecoveryKeys();
  const { mutateAsync: getSafeConfig } = useGetSafeConfig();
  const { mutateAsync: setupRecoveryKey } = useSetupRecoveryKey();

  async function handleGenMultisig() {
    try {
      setIsPending(true);

      let location = await getLocation();
      let retry = 0;

      while (!location || !location?.coords?.accuracy || location.coords.accuracy > 30) {
        retry++;
        if (retry >= 10) break;
        await new Promise((resolve) => setTimeout(resolve, 3000));

        location = await getLocation();
      }

      if (!location || !location?.coords?.accuracy || location.coords.accuracy > 30) return;

      const wallets = await Promise.all(
        recoveryKeys.map(async (recoveryKey) => {
          const w = await createWallet({ recoveryKey, location });
          if (!w) throw new Error('Create wallet failed');

          const wAddress = privateKeyToAddress(`0x${w?.privateKey}` as const);
          return { ...w, address: wAddress };
        })
      );

      setSigners(wallets);
    } catch (error) {
      console.error(error);
    } finally {
      setIsPending(false);
    }
  }

  const handleCopy = async (recoveryKey: string) => {
    if (!recoveryKey) return;

    await Clipboard.setStringAsync(recoveryKey);
  };

  const handleChangeThreshold = (text: string) => {
    if (text.length === 0) {
      setThreshold('');
    }

    if (text.match(/^\d+$/) && Number.parseInt(text) <= signers.length) {
      setThreshold(Number.parseInt(text).toString());
    }
  };

  const handleSetupWallet = async () => {
    try {
      setIsPending(true);

      const firstSigner = signers[0];
      const firstRecoveryKey = recoveryKeys[0];

      const safeVersion = '1.3.0';

      const predictedSafe: PredictedSafeProps = {
        safeAccountConfig: {
          owners: signers.map((signer) => signer.address),
          threshold: Number.parseInt(threshold),
        },
        safeDeploymentConfig: {
          safeVersion,
        },
      };

      let protocolKit = await Safe.init({
        provider: CURRENT_CHAIN.rpcUrls.default.http[0],
        predictedSafe: predictedSafe,
      });

      const isDeployed = await protocolKit.isSafeDeployed();
      const signer = pkToAccount(`0x${firstSigner.privateKey}` as const);
      let safeAddress = await protocolKit.getAddress();

      if (!isDeployed) {
        const deploySafeReceipt = await deploySafe({ protocolKit, signer });
        if (deploySafeReceipt.status !== 'success') {
          throw new Error('Deploy safe failed');
        }

        safeAddress = getSafeAddressFromDeploymentTx(deploySafeReceipt, safeVersion);
      }

      // Connect to the safe
      protocolKit = await protocolKit.connect({
        safeAddress,
      });

      const isModuleEnabled = await protocolKit.isModuleEnabled(env.GEOSAFE_CONTRACT_ADDRESS);
      if (!isModuleEnabled) {
        const enableSafeModuleReceipt = await enableSafeModule({
          signer,
          safeAddress,
          protocolKit,
          ownersPk: signers.map((signer) => `0x${signer.privateKey}` as const),
        });
        if (enableSafeModuleReceipt?.status !== 'success') {
          throw new Error('Enable safe module failed');
        }
      }

      // Register safe
      const { isRegistered } = await getSafeConfig({ safeAddress });
      if (!isRegistered) {
        const registerSafeReceipt = await registerSafe({
          minKeysRequired: BigInt(Number.parseInt(threshold)),
          safeAddress,
          signer,
        });

        if (registerSafeReceipt.status !== 'success') {
          throw new Error('Register safe failed');
        }
      }

      const recoveryKeysMap = await Promise.all(
        recoveryKeys.map(async (recoveryKey, index) => {
          const key: TRecoveryKey = {
            keyId: `0x${recoveryKey}`,
            safe: safeAddress,
            expectedAddress: signers[index].address,
            validUntilBlock: BigInt(DEFAULT_VALIDITY_BLOCKS),
            isUsed: false,
            purpose: RecoveryPurpose.BOTH,
            hint: '',
            stretchedCount: signers[index].stretchingCount,
          };

          return key;
        })
      );

      // Add recovery keys
      const addRecoveryKeysReceipt = await addRecoveryKeys({
        keys: recoveryKeysMap,
        safeAddress,
        signer,
      });

      if (addRecoveryKeysReceipt.status !== 'success') {
        throw new Error('Add recovery keys failed');
      }

      // Store safe address local
      setSafeWallet(safeAddress);

      // Store first signer local
      await setupRecoveryKey(firstRecoveryKey);
      await setupWallet({
        privateKey: firstSigner.privateKey,
        seedPhrase: firstSigner.seedPhrase,
        recoveryKey: firstRecoveryKey,
      });

      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: '(app)' }],
        })
      );
    } catch (error) {
      console.error(error);
    } finally {
      setIsPending(false);
    }
  };

  return (
    <KeyboardAvoidingView behavior='padding' style={styles.fullFlex}>
      <SafeAreaView edges={['bottom']} style={styles.container}>
        <FlatList
          data={signers}
          renderItem={({ item, index }) => (
            <View style={styles.containerKey} key={item.address} pointerEvents='auto'>
              <View style={styles.boxKey} pointerEvents='auto'>
                <ThemedText type='tinyLight' style={styles.keyTitle}>
                  {`Signer Address ${index + 1}`}
                </ThemedText>

                <ThemedText>{item.address}</ThemedText>
              </View>

              <CustomButton type='secondary' onPress={() => handleCopy(item.address)}>
                <Icons.Copy size={20} color='#fff' />
              </CustomButton>
            </View>
          )}
          keyExtractor={(item) => item.address}
          contentContainerStyle={{ gap: 16 }}
          showsVerticalScrollIndicator={false}
        />

        <Spacer height={16} />

        <TextInput
          label='Threshold'
          editable={signers.length > 0}
          keyboardType='number-pad'
          value={threshold}
          onChangeText={handleChangeThreshold}
        />

        <Spacer height={16} />

        <CustomButton
          type='primary'
          onPress={signers.length === 0 ? handleGenMultisig : handleSetupWallet}
          disabled={isPending || (Number(signers.length) > 0 && Number(threshold || 0) <= 0)}
          isLoading={isDeployingSafe || isPending}
        >
          {signers.length === 0 ? 'Generate multisig' : 'Setup wallet'}
        </CustomButton>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const useStyles = () => {
  const white05 = useTheme('white05');
  const white15 = useTheme('white15');
  const white35 = useTheme('white35');

  const styles = StyleSheet.create({
    containerKey: {
      backgroundColor: white05,
      borderWidth: 1,
      borderColor: white15,
      borderRadius: 16,
      padding: 16,
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    boxKey: {
      flex: 1,
      flexDirection: 'column',
      gap: 2,
    },
    keyTitle: {
      color: white35,
    },
    container: {
      flex: 1,
      padding: 16,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
    fullFlex: {
      flex: 1,
    },
  });

  return { styles };
};
