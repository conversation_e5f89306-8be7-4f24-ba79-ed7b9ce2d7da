import { StyleSheet, View } from 'react-native';
import { StepProgress } from '@/components/StepProgress';
import { ThemedText } from '@/components/ThemedText';
import { useTheme } from '@/hooks/useThemeColor';
import { StepAction } from './StepAction';

type Props = {};

export const StepWalletCreation = (props: Props) => {
  const { styles } = useStyles();

  // const handleCreateWallet = () => {
  //   router.navigate('/(new-wallet)/set-up-wallet');
  // };

  // const handleSecureWallet = () => {};

  return (
    <View style={styles.step}>
      <View style={styles.stepBox}>
        <StepProgress isActive={true} />
      </View>

      <View style={styles.container}>
        <ThemedText type='tinyMedium' style={styles.stepText}>
          Step 2
        </ThemedText>

        <ThemedText type='defaultSemiBold'>Wallet setup</ThemedText>

        <StepAction
          hasDone={false}
          title='Create Wallet'
          description='Set up multi sig wallet'
          // onPress={handleCreateWallet}
        />

        <StepAction
          hasDone={false}
          title='Secure wallet'
          description='Complete wallet request'
          // onPress={handleSecureWallet}
        />
      </View>
    </View>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    stepBox: {
      flexDirection: 'row',
      paddingLeft: 16,
      paddingRight: 12,
      paddingTop: 4,
    },
    step: {
      flexDirection: 'row',
      alignItems: 'stretch',
    },
    stepText: {
      color: primary,
    },
    container: {
      gap: 8,
      flex: 1,
    },
  });

  return { styles };
};
