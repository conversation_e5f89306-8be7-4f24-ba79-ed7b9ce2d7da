import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { NumberKeyboard } from '@/components/NumberKeyboard';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useSetupPin } from '@/hooks/useSetupPin';
import { useTheme } from '@/hooks/useThemeColor';
import { Href, router } from 'expo-router';

type Props = {
  nextScreen: string;
};

export const SetUpPin = ({ nextScreen }: Props) => {
  const [pin, setPin] = useState('');
  const [isConfirmingPin, setIsConfirmingPin] = useState(false);
  const [confirmPin, setConfirmPin] = useState('');
  const { mutateAsync: setupPin } = useSetupPin();

  const { styles } = useStyles();

  const handlePressNum = (num: number) => {
    if (isConfirmingPin) {
      setConfirmPin((prev) => prev + num);
      return;
    }
    setPin((prev) => prev + num);
  };

  const handlePressBackspace = () => {
    if (isConfirmingPin) {
      setConfirmPin((prev) => prev.slice(0, -1));
      return;
    }
    setPin((prev) => prev.slice(0, -1));
  };

  useEffect(() => {
    if (pin.length === 6) {
      setIsConfirmingPin(true);
    } else {
      setIsConfirmingPin(false);
    }
  }, [pin]);

  useEffect(() => {
    if (confirmPin.length !== 6) return;

    (async () => {
      try {
        if (confirmPin === pin) {
          await setupPin(pin);
          await new Promise((resolve) => setTimeout(resolve, 500));
          router.navigate({
            pathname: nextScreen
          });
        } else {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          setConfirmPin('');
          setPin('');
          setIsConfirmingPin(false);
        }
      } catch (error) {
        console.error(error);
      }
    })();
  }, [confirmPin, pin, setupPin]);

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <ThemedText type='title' style={styles.title}>
          Enter PIN
        </ThemedText>

        <Spacer height={8} />

        <ThemedText type='smallLight' style={styles.description}>
          Enter a secure pin to access your account
        </ThemedText>

        <Spacer height={24} />

        <View>
          <View style={styles.dotContainer}>
            {Array(6)
              .fill(0)
              .map((_, index) => (
                <View
                  key={index}
                  style={[styles.dot, index < (isConfirmingPin ? confirmPin?.length : pin?.length) && styles.dotActive]}
                />
              ))}
          </View>

          <Show when={pin.length === 6 && confirmPin === pin}>
            <View style={styles.containerTextSuccess}>
              <ThemedText type='defaultLight' style={styles.textSuccess}>
                PIN set successfully
              </ThemedText>
            </View>
          </Show>
        </View>

        <Spacer height={88} />

        <NumberKeyboard onPressNum={handlePressNum} onPressBackspace={handlePressBackspace} />
      </View>
    </View>
  );
};

const useStyles = () => {
  const backgroundColor = useTheme('background');
  const Blue500 = useTheme('Blue/500');

  const styles = StyleSheet.create({
    containerTextSuccess: {
      position: 'absolute',
      bottom: -24,
      left: 0,
      right: 0,
      alignItems: 'center',
      transform: [{ translateY: 24 }],
    },
    textSuccess: {
      color: Blue500,
    },
    dotActive: {
      borderWidth: 0,
      backgroundColor: '#fff',
    },
    dot: {
      width: 18,
      height: 18,
      borderWidth: 1,
      borderColor: '#B7B7C7',
      borderRadius: 999,
      overflow: 'hidden',
    },
    dotContainer: {
      flexDirection: 'row',
      gap: 12,
      justifyContent: 'center',
    },
    container: {
      flex: 1,
      padding: 24,
      paddingTop: 56,
      backgroundColor,
    },
    headerContainer: {
      paddingHorizontal: 16,
    },
    title: {
      textAlign: 'center',
    },
    description: {
      // color: white90,
      textAlign: 'center',
    },
  });

  return { styles };
};
