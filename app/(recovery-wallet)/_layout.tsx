import { Stack } from 'expo-router';
import { StyleSheet } from 'react-native';
import { useTheme } from '@/hooks/useThemeColor';

function RecoveryWalletLayout() {
  const { styles } = useStyles();

  return (
    <Stack
      screenOptions={{
        headerStyle: styles.headerStyle,
        contentStyle: styles.container,
        headerTitleAlign: 'center',
        headerTintColor: '#fff',
        headerBackButtonDisplayMode: 'minimal',
        headerBackButtonMenuEnabled: false,
      }}
    >
      <Stack.Screen name='recovery-wallet-steps' options={{ title: '' }} />
    </Stack>
  );
}

export default RecoveryWalletLayout;

const useStyles = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    headerStyle: {
      backgroundColor: backgroundColor,
    },
  });

  return { styles };
};
