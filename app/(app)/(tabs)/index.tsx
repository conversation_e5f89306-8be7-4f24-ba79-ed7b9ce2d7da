import { StyleSheet, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { useTheme } from '@/hooks/useThemeColor';

export default function HomeScreen() {
  const { styles } = useStyles();

  return (
    <View style={styles.container}>
      <ThemedText>Home</ThemedText>
    </View>
  );
}

const useStyles = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
  });

  return { styles };
};
